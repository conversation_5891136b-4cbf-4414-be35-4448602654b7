import { useMenuStore } from '@/stores/index.js'
import { storeToRefs } from "pinia";

/**
 * staticRouter (静态路由)
 */
export const staticRouter = [
    {
        path: "/layout",
        name: 'layout',
        //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
        meta: { title: '' },
        component: () => import("@/views/layout/index.vue"),

        // redirect: HOME_URL
        children: [
            {
                path: "/home",
                name: "home",
                id: 0,
                type: 10, // 改为有子菜单的菜单组类型
                titleName: "工作台",
                meta: { title: '' ,no_scroll:true},
                icon: "House",
                component: () => import("@/views/modules/home/<USER>"),
                children: [
                    {
                        path: "/AIDubbing",
                        name: "AIDubbing",
                        id: 10,
                        type: 1,
                        titleName: "AI 配音",
                        meta: { title: '' ,fixedHeader: true,no_scroll:true},
                        component: () => import("@/views/modules/home/<USER>")
                    },
                    {
                        path: "/commercialDubbing",
                        name: "commercialDubbing", 
                        id: 11,
                        type: 1,
                        titleName: "AI商配",
                        meta: { title: '' ,fixedHeader: true,no_scroll:true},
                        component: () => import("@/views/modules/home/<USER>")
                    },
                    {
                        path: "/Editor",
                        name: "Editor",
                        id: 12,
                        type: 1,
                        titleName: "一键成片",
                        meta: { title: '' ,no_scroll:true},
                        component: () => import("@/views/modules/home/<USER>")
                    },
                    {
                        path: "/CloudEdit",
                        name: "CloudEdit",
                        id: 13,
                        type: 4, // 改为URL类型
                        titleName: "专业云剪",
                        meta: { title: '' ,no_scroll:true},
                        url: "https://yunjian.peiyinbangshou.com/App", // 专业云剪的URL
                        component: () => import("@/views/modules/common/EmptyComponent.vue")
                    },
                    {
                        path: "/digital-human-editor",
                        name: "DigitalHumanEditor",
                        id: 14,
                        type: 1,
                        titleName: "数字人",
                        meta: { title: '数字人', no_scroll: true },
                        component: () => import("@/views/modules/digitalHuman/DigitalHumanEditorPage.vue")
                    }
                ],
            },

            // {
            //     path: "/realVoice",
            //     name: "realVoice",
            //     id: 100,
            //     type: 1,
            //     titleName: "真人配音",
            //     icon: "Microphone",
            //     meta: { title: '', fixedHeader: true },
            //     component: () => import("@/views/modules/realVoice/index.vue"),
            // },

            // {
            //     path: "/soundStore",
            //     name: "soundStore",
            //     id: 101,
            //     type: 1,
            //     titleName: "音色商店",
            //     icon: "ShoppingBag",
            //     meta: { title: '', fixedHeader: true },
            //     component: () => import("@/views/modules/soundStore/index.vue"),
            // },

            // {
            //     path: "/membership",
            //     name: "membership",
            //     id: 102,
            //     type: 1,
            //     titleName: "会员计划",
            //     icon: "Star",
            //     meta: { title: '' },
            //     component: () => import("@/views/account/membership.vue"),
            // },

            // {
            //     path: "/test",
            //     name: 'test',
            //     type: 1,
            //     id:1,
            //     titleName: "ai配音",
            //     icon: "home",
            //     //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
            //     meta: { title: '' },
            //     component: () => import("@/views/modules/test/index.vue"),
            // },
            {
                path: '/',
                name: '/',
                id: 1,
                type: 10,
                titleName: 'AI工具',
                icon: '/img/Vector1.png',
                children: [
                    {
                        path: '/ParsingVideo',
                        name: 'ParsingVideo',
                        id: 2,
                        type: 1,
                        titleName: '解析视频',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/parsing/index.vue')
                    },
                    // 文案提取
                    {
                        path: '/Extraction',
                        name: 'Extraction',
                        id: 3,
                        type: 1,
                        titleName: '文案提取',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/extraction/index.vue')
                    },
                    // 去水印
                    {
                        path: '/WaterMark',
                        name: 'watermark',
                        id: 4,
                        type: 1,
                        titleName: '去水印字幕',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/watermark/index.vue')
                    },
                    // 智能加字幕
                    // {
                    //     path: '/CapTions',
                    //     name: 'captions',
                    //     id: 5,
                    //     type: 1,
                    //     titleName: '智能加字幕',
                    //     component: () => import('@/views/modules/captions/index.vue')
                    // },

                    // 文案创作
                    {
                        path: '/ComPose',
                        name: 'compose',
                        id: 5,
                        type: 1,
                        titleName: '文案创作',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/compose/altogether.vue'),
                    },

                    // 声音克隆
                    {
                        path: '/VoiceClone',
                        name: 'voiceClone',
                        id: 6,
                        type: 1,
                        titleName: '声音克隆',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/voiceClone/index.vue')
                    },

                    // 测试
                    // {
                    //     path:'/tt',
                    //     name:'tt',
                    //     id:5,
                    //     type:1,
                    //     titleName:'测试',
                    //     component: () => import('@/views/modules/compose/index1.vue')
                    // }
                ]
            },
            {
                path: '/mySpace',
                name: 'mySpace',
                id: 6,
                type: 10,
                titleName: '我的空间',
                icon: '/img/People1.png',
                // component: () => import('@/views/modules/mySpace/index.vue'),
                // redirect: '/mySpace/myWorks',
                children: [
                    {
                        path: 'myWorks',
                        name: 'myWorks',
                        id: 7,
                        type: 1,
                        titleName: '我的作品',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/mySpace/myWorks/index.vue')
                    },
                    {
                        path: 'myMaterials',
                        name: 'myMaterials',
                        id: 8,
                        type: 1,
                        titleName: '我的素材',
                        meta: { title: '' ,no_scroll:true},
                        component: () => import('@/views/modules/mySpace/myMaterials/index.vue')
                    },
                    // {
                    //     path: 'myCustomizations',
                    //     name: 'myCustomizations',
                    //     id: 9,
                    //     type: 1,
                    //     titleName: '我的定制',
                    //     component: () => import('@/views/modules/mySpace/myCustomizations/index.vue')
                    // }
                ]
            },

            // 将我的收益改回单个菜单项
            {
                path: "/income",
                name: "income",
                id: 99, // 给一个不重复的ID
                type: 1, // 改回单个菜单项类型
                titleName: "我的收益",
                icon: "Money", // 使用Element Plus的Money图标组件名称
                component: () => import("@/views/modules/income/index.vue"),
            },

            // 将真人配音菜单项添加到侧边栏
            {
                path: "/realVoice-nav",
                name: "realVoice-nav",
                id: 100,
                type: 4, // MenuType.URL类型，表示外部链接
                titleName: "真人配音",
                icon: "Microphone",
                // 使用URL属性实现外部链接功能
                url: "/realVoice", // 外部链接路径
                meta: { title: '' },
                component: () => import("@/views/modules/common/EmptyComponent.vue"),
            },

            // 将音色商店改回单个菜单项
            {
                path: "/soundStore-nav",
                name: "soundStore-nav",
                id: 101,
                type: 4, // MenuType.URL类型，表示外部链接
                titleName: "音色商店",
                icon: "/img/Application_(应用).png",
                // 使用URL属性实现外部链接功能
                url: "/soundStore", // 外部链接路径
                meta: { title: '' },
                component: () => import("@/views/modules/common/EmptyComponent.vue"),
            },

            // 将会员计划改回单个菜单项
            {
                path: "/membership-nav",
                name: "membership-nav",
                id: 102,
                type: 4, // MenuType.URL类型，表示外部链接
                titleName: "会员计划",
                icon: "/img/Diamonds_(钻石).png",
                // 使用URL属性实现外部链接功能
                url: "/membership", // 外部链接路径
                meta: { title: '',no_scroll:true },
                component: () => import("@/views/modules/common/EmptyComponent.vue"),
            },

            // API服务菜单项
            {
                path: "/apiService",
                name: "apiService",
                id: 104,
                type: 4, // MenuType.URL类型，表示外部链接
                titleName: "API服务",
                icon: "/img/API1.png", // API服务未选中状态图标
                // 使用URL属性实现外部链接功能
                url: "/apiService", // 外部链接路径
                meta: { title: '',no_scroll:true },
                component: () => import("@/views/modules/common/EmptyComponent.vue"),
            },



        ],

    },





    // ai配音
    {
        path: "/mainPage",
        name: 'mainPage',
        type: 1,
        titleName: "首页",
        // icon: "home",
        //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
        meta: { title: '' },
        component: () => import("@/views/modules/mainPage/index.vue"),
        // redirect: HOME_URL
        children: [

            {
                path: "/soundStore",
                name: 'soundStore',
                type: 1,
                titleName: "音色商店",
                
                // icon: "home",
                //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
                meta: { title: '',fixedHeader:true, },
                component: () => import("@/views/modules/soundStore/index.vue"),
            },

            // {
            //     path: "/realVoice",
            //     name: 'realVoice',
            //     type: 1,
            //     titleName: "真人配音",
            //     // icon: "home",
            //     //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
            //     meta: { title: '' ,fixedHeader:true,},
            //     component: () => import("@/views/modules/realVoice/index.vue"),
            // },
            {
                path: "/AIDubbing",
                name: 'AIDubbing',
                type: 1,
                // titleName: "ai配音",
                // icon: "home",
                //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
                meta: { title: '',hideBack:true,no_scroll:true,fixedHeader: true },
                component: () => import("@/views/modules/AIDubbing/index.vue"),
            },
            {
                path: "/commercialDubbing",
                name: 'commercialDubbing',
                type: 1,
                // titleName: "ai配音",
                // icon: "home",
                //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
                meta: { title: '',fixedHeader: true,no_scroll:true },
                component: () => import("@/views/modules/commercialDubbing/index.vue"),
            },


        ]
    },

    // 添加音色商店路由，作为单独页面
    {
        path: "/soundStore",
        name: "soundStore",
        id: 101,
        type: 1,
        titleName: "音色商店",
        icon: "ShoppingBag",
        meta: { title: '', fixedHeader: true },
        component: () => import("@/views/modules/soundStore/index.vue"),
    },
// header_module:'dark'
    // 添加真人配音路由，作为单独页面
    {
        path: "/realVoice",
        name: "realVoice",
        id: 100,
        type: 1,
        titleName: "真人配音",
        icon: "Microphone",
        meta: { title: '', fixedHeader: true, no_scroll: true },
        component: () => import("@/views/modules/realVoice/index.vue"),
    },

    // 添加会员计划路由，作为单独页面
    {
        path: "/membership",
        name: "membership",
        id: 102,
        type: 1,
        titleName: "会员计划",
        icon: "Star",
        meta: { title: '',no_scroll:true  },
        component: () => import("@/views/account/membership.vue"),
    },

    // 添加收益页面路由，作为单独页面
    {
        path: "/income",
        name: "income-page",
        id: 103,
        type: 1,
        titleName: "我的收益",
        icon: "Money",
        meta: { title: '' },
        component: () => import("@/views/modules/income/index.vue"),
    },

    {
        path: '/Editor',
        name: 'Editor',

        type: 1,
        titleName: '测试',
        // 这个文件不存在，注释掉或更改为正确路径
        // component: () => import('@/views/Editor/index.vue'),
        // 重定向到ContentCreation作为临时解决方案
        redirect: '/ContentCreation'
    },

    //
    {
        path: '/myWorks',
        redirect: '/mySpace/myWorks'
    },
    {
        path: '/myMaterials',
        redirect: '/mySpace/myMaterials'
    },
    {
        path: '/myCustomizations',
        redirect: '/mySpace/myCustomizations'
    },



    // {
    //     path: '/MusicAudio',
    //     name: 'MusicAudio',
    //     type: 1,
    //     titleName: '音乐音效',
    //     meta: { title: '' ,no_scroll:true},
    //     component: () => import('@/views/Editor/MusicAudio/index.vue'),
    // },

    {
        path: '/H4Home',
        name: 'H4Home',
        type: 1,
        titleName: 'H4首页',
        meta: { fixedHeader: true },
        component: () => import('@/views/modules/H4Home/index.vue'),
    },

    {
        path: '/ContentCreation',
        name: 'ContentCreation',
        type: 1,
        titleName: '文案创作',
        meta: { title: '' ,no_scroll:true},
        component: () => import('@/views/Editor/ContentCreation/index.vue'),
    },

    {
        path: '/VoiceOver',
        name: 'VoiceOver',
        type: 1,
        titleName: '配音',
        meta: { title: '' ,no_scroll:true},
        component: () => import('@/views/Editor/VoiceOver/index.vue'),
    },

    {
        path: '/VideoEditing',
        name: 'VideoEditing',
        type: 1,
        titleName: '视频剪片',
        meta: { title: '' ,no_scroll:true},
        component: () => import('@/views/Editor/VideoEditing/index.vue'),
    },
    {
        path: '/userAgreement',
        name: 'userAgreement',
        type: 1,
        titleName: '用户协议',
        meta: { title: '' ,no_scroll:true},
        component: () => import('@/views/modules/home/<USER>'),
    },
    {
        path: '/privacyAgreement',
        name: 'privacyAgreement',
        type: 1,
        titleName: '隐私协议',
        component: () => import('@/views/modules/home/<USER>'),
        path:'/agreement',
        name:'agreement',
        titleName:'',
        meta: { title: '', fixedHeader: true },
        component: () => import('@/views/modules/home/<USER>'),
    },
    {
        path: "/allRealVoice",
        name: 'allRealVoice',
        type: 1,
        titleName: ' ',
        meta: { fixedHeader:true,},
        component: () => import("@/views/modules/realVoice/allRealVoice.vue"),
    },
    {
        path: "/realVoiceDetail",
        name: 'realVoiceDetail',
        type: 1,
        titleName: ' ',
        meta: { fixedHeader:true,},
        component: () => import("@/views/modules/realVoice/detail.vue"),
    }, {
        path: "/usageStatistics",
        name: 'usageStatistics',
        type: 1,
        titleName: ' ',
        meta: { title: '', fixedHeader: true ,no_scroll:true},
        component: () => import("@/views/account/usage_statistics.vue")
    },
    {
        path: "/apiService",
        name: "apiService",
        type: 1,
        titleName: "API服务",
        meta: { title: '', fixedHeader: true },
        component: () => import("@/views/modules/apiService/index.vue"),
    },

    // 数字人编辑器
    {
        path: "/digital-human-editor",
        name: "DigitalHumanEditor",
        type: 1,
        titleName: "数字人编辑器",
        meta: { title: '数字人编辑器', no_scroll: true },
        component: () => import("@/views/modules/digitalHuman/DigitalHumanEditorPage.vue"),
    },
    {
        path: "/cloneService",
        name: "cloneService",
        type: 1,
        titleName: "克隆服务",
        meta: { title: '', fixedHeader: true },
        component: () => import("@/views/modules/cloneService/index.vue"),
    },



    // {
    //     // path: LOGIN_URL,
    //     name: "login",
    //     component: () => import("@/views/login/index.vue"),
    //     meta: {
    //         title: "登录"
    //     }
    // },
    // {
    //     path: "/layout",
    //     name: "layout",
    //     component: () => import("@/layouts/index.vue"),
    //     // component: () => import("@/layouts/indexAsync.vue"),
    //     redirect: HOME_URL,
    //     children: []
    // }
];

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
    // {
    //     path: "/403",
    //     name: "403",
    //     component: () => import("@/components/ErrorMessage/403.vue"),
    //     meta: {
    //         title: "403页面"
    //     }
    // },
    // {
    //     path: "/404",
    //     name: "404",
    //     component: () => import("@/components/ErrorMessage/404.vue"),
    //     meta: {
    //         title: "404页面"
    //     }
    // },
    // {
    //     path: "/500",
    //     name: "500",
    //     component: () => import("@/components/ErrorMessage/500.vue"),
    //     meta: {
    //         title: "500页面"
    //     }
    // },
    // // Resolve refresh page, route warnings
    // {
    //     path: "/:pathMatch(.*)*",
    //     component: () => import("@/components/ErrorMessage/404.vue")
    // }
];
