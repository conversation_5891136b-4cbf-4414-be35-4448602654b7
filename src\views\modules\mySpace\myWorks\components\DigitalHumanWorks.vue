<template>
    <div class="digitalWorksBox">
        <div class="cue">您的作品将为您保存30天，及时下载保存！</div>
        <div class="worksBox" v-infinite-scroll="loadDigitalWorksList" style="overflow: auto">
            <div class="project" v-for="(item, index) in digitalList" :key="index">
                <!-- 生成中动画 -->
                <div class="mask" v-if="item.status == 0">
                    <img src="@/assets/img/waiting.gif" />
                    <p>生成中，请稍候...</p>
                </div>
                <!-- 失败状态 -->
                <div class="list" v-if="item.status == 2">
                    <div class="audioCover"><img src="@/assets/img/digital.png" style="width: 100%;height: 100%;" />
                    </div>
                    <div class="mask_info">
                        <img src="@/assets/img/delete.png" @click="deleteDigital(item)" />
                        <div class="edit" @click="reEdit(item.id)">重新编辑</div>
                        <div class="failureCue">失败</div>
                    </div>
                </div>
                <!-- 成功状态 -->
                <div class="list" v-if="item.status == 1">
                    <div class="audioCover" v-if="item.screenWidth == '1080'"><img :src="item.previewUrl" alt=""
                            style="width: 100%;height: 100%;" />
                    </div>
                    <div class="audioCover_h" v-if="item.screenWidth == '1920'"><img :src="item.previewUrl" alt=""
                            style="width: 100%;height: 100%;" />
                    </div>
                    <el-dropdown @click.stop @command="command => handleDropdownAction(command, item)">
                        <img src="@/assets/img/三个点.png" class="more-icon" alt="更多" />
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="rename">
                                    <el-icon>
                                        <EditPen />
                                    </el-icon>重命名
                                </el-dropdown-item>
                                <el-dropdown-item command="delete">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>删除
                                </el-dropdown-item>
                                <el-dropdown-item command="move">
                                    <el-icon>
                                        <Position />
                                    </el-icon>移动文件夹
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <p class="time">{{ formatTime(item.duration) }}</p>
                    <img src="@/assets/img/playButton.png" alt="" class="playButtonImg" @click="watchVideo(item)" />
                </div>
                <div class="info">
                    <div>
                        <p class="title">{{ item.title }}</p>
                        <p class="subtitle">{{ item.updatedTime }}</p>
                    </div>
                    <img style="cursor: pointer;" v-if="item.status == 1" src="@/assets/img/down.png"
                        @click="downLoadVideo(item)" alt="" />
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog v-model:visible="deleteDialogVisible" :item-title="choosedItem?.title"
        @cancel="handleDeleteCancel" @confirm="handleDeleteConfirm" :show-warning="false" />
    <!-- 重命名对话框 -->
    <RenameDialog v-model:visible="renameDialogVisible" title="修改作品名称" @confirm="handleRenameConfirm" />
    <!-- 移动到项目对话框 -->
    <DigitalMoveToDialog v-model:visible="moveDialogVisible" :selected-item="choosedItem" :project-list="projects"
        @confirm="handleMoveConfirm" />
    <!-- 视频播放对话框 -->
    <DigitalVideoDialog v-model:visible="mediaPlayerDialogVisible" :title="choosedItem?.title"
        :mediaSource="choosedItem?.digitalHumanUrl" :screenWidth="choosedItem?.screenWidth"
        @close="handleMediaDialogClose" @down="downLoadVideo" />
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, nextTick } from 'vue'
import { Delete, EditPen, Position } from '@element-plus/icons-vue'
import { getDigitalHumanWorksList, updateWorksStatus, deleteDigitalHumanWorks, renameDigitalHumanWorks, getAlbumsByCreator } from '@/api/mySpace.js'
import { useloginStore } from '@/stores/login'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import DeleteConfirmDialog from '../../components/DeleteConfirmDialog.vue'
import RenameDialog from '../../components/RenameDialog.vue'
import DigitalMoveToDialog from '../../components/DigitalMoveToDialog.vue'
import DigitalVideoDialog from '../../components/DigitalVideoDialog.vue'

const props = defineProps({ selectedFileId: {}, workTab: {} })
const router = useRouter()
const currentPage = ref(1) //数字人作品列表分页
const currentPageSize = ref(30)
const totalPage = ref('')
const digitalList = ref(null)
const generatingList = ref(null) //生成中状态列表
const projects = ref(null) //项目列表
const choosedItem = ref({})
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}
const deleteDialogVisible = ref(false)
const renameDialogVisible = ref(false)
const moveDialogVisible = ref(false)
const timer = ref(null)
const mediaPlayerDialogVisible = ref(false)

// 获取数字人作品列表
const getDigitalList = async () => {
    const { records, pages } = await getDigitalHumanWorksList({ userId: getUserId(), pageParam: { pageNum: currentPage.value, pageSize: currentPageSize.value }, albumId: props.selectedFileId ? props.selectedFileId : '' })
    totalPage.value = pages
    if (currentPage.value == 1) {
        digitalList.value = records
    } else {
        digitalList.value = digitalList.value.concat(records)
    }
    generatingList.value = digitalList.value.filter(item => item.status == '0')
    if (generatingList.value.length > 0) {
        // 生成中状态更新
        timer.value = setInterval(() => {
            const ids = generatingList.value.map(item => item.id)
            if (ids.length > 0) {
                updateDigitalList(ids)
            } else {
                clearInterval(timer.value)
                timer.value = null
            }
        }, 30000)
    }
}
// 根据生成中状态id去更新状态
const updateDigitalList = async (idArr) => {
    const data = await updateWorksStatus({ ids: idArr })
    if (data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            if (data[i].status != 0) {
                for (let j = 0; j < digitalList.value.length; j++) {
                    if (data[i].id == digitalList.value[j].id) {
                        digitalList.value[j] = data[i]
                        break
                    }
                }
            }
        }
        generatingList.value = data.filter(item => item.status == 0)
    } else {
        clearInterval(timer.value)
        timer.value = null
    }

}
// 数字人作品滚动加载
const loadDigitalWorksList = () => {
    if (parseFloat(currentPage.value) < parseFloat(totalPage.value)) {
        currentPage.value++
        getDigitalList()
    }
}
// 时间秒数换算
const formatTime = (seconds) => {
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
// 重新编辑
const reEdit = (id) => {
    router.push({ path: '/digital-human-editor', query: { id: id } });
}
// 删除数字人作品
const deleteDigital = (item) => {
    choosedItem.value = item
    deleteDialogVisible.value = true
}
const handleDeleteCancel = () => {
    // console.log('删除对话框取消')
}
const handleDeleteConfirm = async () => {
    const data = await deleteDigitalHumanWorks({ id: choosedItem.value.id })
    currentPage.value = 1
    getDigitalList()
    ElMessage.success(data)

}
// 重命名数字人作品
const handleRenameConfirm = async (newName) => {
    const data = await renameDigitalHumanWorks({ id: choosedItem.value.id, title: newName })
    currentPage.value = 1
    getDigitalList()
    ElMessage.success(data)
}
const handleDropdownAction = (command, item) => {
    choosedItem.value = item
    if (command == 'rename') {
        renameDialogVisible.value = true
    } else if (command == 'delete') {
        deleteDialogVisible.value = true
    } else if (command == 'move') {
        moveDialogVisible.value = true
    }
}
// 加载项目文件夹列表
const loadProjectsList = async () => {
    const response = await getAlbumsByCreator({ userId: getUserId() })
    projects.value = response
}
// 确认移动文件夹
const handleMoveConfirm = () => {
    currentPage.value = 1
    getDigitalList()
}
// 下载视频
let loadingInstance = null
const downLoadVideo = (data) => {
    loadingInstance = ElLoading.service({ fullscreen: true, text: '下载中...' })
    if (data.url && data.url != undefined) {
        downloadVideoMethod(data.url, data.filename)
    } else {
        downloadVideoMethod(data.digitalHumanUrl, data.title)
    }
}
const downloadVideoMethod = async (url, filename) => {
    try {
        const response = await fetch(url);
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = blobUrl;
        a.download = filename || 'video.mp4';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        // Clean up
        URL.revokeObjectURL(blobUrl);
        nextTick(() => {
            loadingInstance.close()
            ElMessage.success('下载成功')
        })
    } catch (error) {
        console.error('Download failed:', error);
    }
}
// 根据选择文件夹的不同切换列表
watch(() => props.selectedFileId, () => {
    currentPage.value = 1
    getDigitalList()
})
watch(() => props.workTab, () => {
    currentPage.value = 1
    getDigitalList()
}, { deep: true, immediate: true })

onMounted(() => {
    loadProjectsList()
})

onBeforeUnmount(() => {
    if (timer.value) {
        clearInterval(timer.value)
        timer.value = null
    }
})
// 查看视频
const watchVideo = (item) => {
    choosedItem.value = item
    mediaPlayerDialogVisible.value = true
}
const handleMediaDialogClose = () => {
    mediaPlayerDialogVisible.value = false
}

</script>

<style lang="scss" scoped>
* {
    box-sizing: border-box;
}

p {
    margin: 0;
}

.digitalWorksBox {
    height: 100%;
    .cue {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin: 10px 0 20px 0;
    }

    .worksBox {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        color: #000000;
        height: calc(100% - 50px);
        scrollbar-width: none; //Firefox
        -ms-overflow-style: none; //IE 和 Edge

        .project {
            margin-right: 30px;

            .list,
            .mask {
                width: 231px;
                height: 160px;
                border-radius: 4px;
                position: relative;
                background: #F1F2F4;
            }

            .mask {
                text-align: center;
                padding-top: 40px;

                p {
                    text-align: center;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.65);
                }
            }

            .list {
                display: flex;
                position: relative;

                .audioCover {
                    width: 100px;
                    margin: 0 auto;
                }

                .audioCover_h {
                    width: 100%;
                    height: 100%;
                }

                .time {
                    width: 40px;
                    height: 21px;
                    border-radius: 2px;
                    background: #000000;
                    font-size: 12px;
                    color: #FFFFFF;
                    position: absolute;
                    bottom: 7px;
                    left: 7px;
                    text-align: center;
                    line-height: 21px;
                }

                .playButtonImg {
                    width: 40px;
                    height: 40px;
                    position: absolute;
                    top: 60px;
                    left: 95px;
                    cursor: pointer;
                }

                .mask_info {
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.4);
                    position: absolute;
                    top: 0;
                    left: 0;
                    border-radius: 4px;

                    img {
                        position: absolute;
                        top: 8px;
                        right: 8px;
                        cursor: pointer;
                    }

                    .edit {
                        width: 76px;
                        height: 36px;
                        border: 1px solid #FFFFFF;
                        border-radius: 4px;
                        font-size: 14px;
                        color: #FFFFFF;
                        text-align: center;
                        line-height: 36px;
                        margin: 62px auto 0;
                        cursor: pointer;
                    }

                    .failureCue {
                        width: 40px;
                        height: 21px;
                        background: #FF2D55;
                        border-radius: 2px;
                        position: absolute;
                        bottom: 7px;
                        left: 7px;
                        font-size: 12px;
                        color: #FFFFFF;
                        text-align: center;
                        line-height: 21px;
                    }
                }

                .more-icon {
                    width: 14px;
                    height: 14px;
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    cursor: pointer;
                }

                .more-icon:focus {
                    outline: none;
                }
            }

            .info {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 30px;

                .title {
                    font-size: 14px;
                    font-weight: 500;
                    margin: 15px 0 3px 0;
                }

                .subtitle {
                    font-size: 12px;
                }
            }

        }

    }

    .worksBox::-webkit-scrollbar {
        display: none;
    }
}
</style>
<style lang="scss">
.el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: #F2F3F5;
    color: #1D2129;
}

.el-loading-spinner .path {
    stroke: #0AAF60;
}

.el-loading-spinner .el-loading-text {
    color: #0AAF60;
}
</style>
